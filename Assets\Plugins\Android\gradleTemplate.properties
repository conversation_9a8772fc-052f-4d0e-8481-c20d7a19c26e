org.gradle.jvmargs=-Xmx**JVM_HEAP_SIZE**M
org.gradle.parallel=true
android.enableR8=**MINIFY_WITH_R_EIGHT**
unityStreamingAssets=**STREAMING_ASSETS**

#com.android.tools.build:gradle:7.2.0
#gradle版本7.2需要jdk11
org.gradle.java.home=D\:\\Android\\jdk11

#ironSource
android.enableDexingArtifactTransform=false

# 解决DEX转换问题
android.enableD8.desugaring=true
android.enableD8=true

# 解决Kotlin版本冲突
kotlin.code.style=official

# Android Resolver Properties Start
android.useAndroidX=true
android.enableJetifier=true
# Android Resolver Properties End
**ADDITIONAL_PROPERTIES**

#https://discussions.unity.com/t/gradle-build-issues-for-android-api-sdk-35-in-unity-2022-3lts/1502187/5
android.aapt2FromMavenOverride=F\:\\AndroidEnv\\AndroidPlayer-bobo-tw\\SDK\\build-tools\\35.0.0\\aapt2.exe